// pages/product/product_library/product_library.js
const api = require('../../../utils/api');
const productTypesBrands = require('../../../config/productTypesBrands');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 搜索相关
    searchKeyword: '',
    searchMode: false, // 是否处于搜索模式

    // 筛选相关
    productTypeOptions: [],
    brandOptions: [{ value: '', label: '全部品牌' }],
    productTypeIndex: -1,
    brandIndex: -1,
    selectedProductType: '',
    selectedBrand: '',

    // 产品列表
    products: [],
    loading: false,
    loadingMore: false,
    hasSearched: false,

    // 分页相关
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    hasMore: false,

    // 滚动位置管理
    scrollTop: 0,
    isLoadingMore: false, // 用于区分是否在加载更多状态

    // 产品对比相关
    compareVisible: false // 对比面板是否显示
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initializeFilters();
    
    // 如果有传入的筛选参数，自动进行搜索
    if (options.productType || options.brand) {
      this.setFiltersFromOptions(options);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新产品列表中的对比状态
    this.updateProductsCompareStatus();

    // 监听全局对比列表更新事件
    const app = getApp();
    app.getEventChannel().on('compareProductsUpdated', this.onGlobalCompareUpdated.bind(this));

    // 监听登录状态变化事件
    app.getEventChannel().on('loginStatusChanged', this.onLoginStatusChanged.bind(this));

    // 通知应用当前页面已显示，用于更新TabBar角标
    if (app && app.onPageShow) {
      app.onPageShow('pages/product/product_library/product_library');
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 移除全局对比列表更新事件监听
    const app = getApp();
    app.getEventChannel().off('compareProductsUpdated', this.onGlobalCompareUpdated.bind(this));

    // 移除登录状态变化事件监听
    app.getEventChannel().off('loginStatusChanged', this.onLoginStatusChanged.bind(this));
  },

  /**
   * 监听全局对比列表更新事件
   */
  onGlobalCompareUpdated() {
    // 当全局对比列表更新时，更新产品列表的对比状态
    this.updateProductsCompareStatus();
  },

  /**
   * 监听登录状态变化事件
   */
  onLoginStatusChanged(isLoggedIn) {
    console.log('产品库页面监听到登录状态变化:', isLoggedIn);

    if (!isLoggedIn) {
      // 用户退出登录时，重置页面状态
      this.resetPageState();
    }
  },

  /**
   * 重置页面状态
   * 在用户退出登录时调用，清除上一个用户的搜索结果和状态
   */
  resetPageState() {
    console.log('重置产品库页面状态');

    // 调用现有的重置筛选方法，但不显示提示信息
    this.setData({
      // 重置搜索状态
      searchKeyword: '',
      searchMode: false,
      // 重置筛选状态
      productTypeIndex: -1,
      brandIndex: -1,
      selectedProductType: '',
      selectedBrand: '',
      brandOptions: [{ value: '', label: '全部品牌' }],
      // 重置产品列表
      products: [],
      hasSearched: false,
      currentPage: 1,
      totalCount: 0,
      hasMore: false,
      scrollTop: 0,
      // 重置加载状态
      loading: false,
      loadingMore: false,
      // 重置对比面板状态
      compareVisible: false
    });

    console.log('产品库页面状态已重置');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (this.data.hasSearched) {
      this.refreshProducts();
    } else {
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 使用 scroll-view 的 bindscrolltolower 事件替代
    // 保留此方法作为备用
  },

  /**
   * scroll-view 滚动到底部事件 - 已禁用自动加载
   */
  onScrollToLower() {
    // 不再自动加载更多，用户需要手动点击"查看更多产品"按钮
    console.log('滚动到底部，等待用户手动加载更多...');
    // 可以在这里添加一些提示或者其他操作
  },

  /**
   * 初始化筛选选项
   */
  initializeFilters() {
    try {
      // 初始化产品类型选项
      const productTypeOptions = [
        { value: '', label: '全部类型' }
      ];
      
      // 从配置文件中读取产品类型
      const productTypes = productTypesBrands.productTypes || {};
      Object.keys(productTypes).forEach(type => {
        let typeLabel = type;
        // 将英文类型转换为中文显示
        switch(type) {
          case 'phone':
            typeLabel = '手机';
            break;
          case 'laptop':
            typeLabel = '笔记本电脑';
            break;
          case 'tablet':
            typeLabel = '平板电脑';
            break;
          case 'headphones':
            typeLabel = '耳机';
            break;
          case 'smartwatch':
            typeLabel = '智能手表';
            break;
          default:
            typeLabel = type;
        }
        productTypeOptions.push({
          value: type,
          label: typeLabel
        });
      });

      this.setData({
        productTypeOptions,
        // 默认选中"全部类型"
        productTypeIndex: 0,
        selectedProductType: '',
        // 默认选中"全部品牌"
        brandIndex: 0,
        selectedBrand: ''
      });

      console.log('产品类型选项初始化完成:', productTypeOptions);
    } catch (error) {
      console.error('初始化筛选选项失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'error'
      });
    }
  },

  /**
   * 从传入参数设置筛选条件
   */
  setFiltersFromOptions(options) {
    const { productType, brand } = options;
    
    // 设置产品类型
    if (productType) {
      const typeIndex = this.data.productTypeOptions.findIndex(item => item.value === productType);
      if (typeIndex >= 0) {
        this.setData({
          productTypeIndex: typeIndex,
          selectedProductType: productType
        });
        this.updateBrandOptions(productType);
      }
    }
    
    // 设置品牌
    if (brand) {
      setTimeout(() => {
        const brandIndex = this.data.brandOptions.findIndex(item => item.value === brand);
        if (brandIndex >= 0) {
          this.setData({
            brandIndex: brandIndex,
            selectedBrand: brand
          });
        }
        // 自动搜索
        this.searchProducts();
      }, 100);
    } else if (productType) {
      // 只有产品类型时也自动搜索
      setTimeout(() => {
        this.searchProducts();
      }, 100);
    }
  },

  /**
   * 产品类型选择事件
   */
  onProductTypeSelect(e) {
    const { value, index } = e.currentTarget.dataset;
    const selectedOption = this.data.productTypeOptions[index];
    
    console.log('选择产品类型:', selectedOption);
    
    this.setData({
      productTypeIndex: index,
      selectedProductType: value,
      // 重置品牌选择
      brandIndex: -1,
      selectedBrand: ''
    });
    
    // 更新品牌选项
    this.updateBrandOptions(value);
  },

  /**
   * 品牌选择事件
   */
  onBrandSelect(e) {
    const { value, index } = e.currentTarget.dataset;
    const selectedOption = this.data.brandOptions[index];
    
    console.log('选择品牌:', selectedOption);
    
    this.setData({
      brandIndex: index,
      selectedBrand: value
    });
  },

  /**
   * 更新品牌选项
   */
  updateBrandOptions(productType) {
    try {
      const brandOptions = [
        { value: '', label: '全部品牌' }
      ];
      
      if (productType && productTypesBrands.productTypes[productType]) {
        const brands = productTypesBrands.productTypes[productType].brands || [];
        brands.forEach(brand => {
          // 过滤掉"未知品牌"
          if (brand !== '未知品牌') {
            brandOptions.push({
              value: brand,
              label: brand
            });
          }
        });
      }
      
      this.setData({
        brandOptions
      });
      
      console.log('品牌选项更新完成:', brandOptions);
    } catch (error) {
      console.error('更新品牌选项失败:', error);
    }
  },

  /**
   * 搜索输入事件
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 搜索输入确认事件（用户按回车）
   */
  onSearchConfirm(e) {
    const keyword = e.detail.value.trim();
    if (keyword) {
      this.performKeywordSearch();
    }
  },

  /**
   * 搜索按钮点击事件
   */
  onSearchButtonTap() {
    const keyword = this.data.searchKeyword.trim();
    if (keyword) {
      this.performKeywordSearch();
    }
  },

  /**
   * 执行关键字搜索
   */
  async performKeywordSearch() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键字',
        icon: 'none'
      });
      return;
    }

    // 切换到搜索模式，重置分页和滚动位置
    this.setData({
      searchMode: true,
      currentPage: 1,
      products: [],
      scrollTop: 0,
      isLoadingMore: false
    });

    await this.loadProducts();
  },

  /**
   * 搜索产品（筛选模式）
   */
  async searchProducts() {
    // 切换到筛选模式，重置分页和滚动位置
    this.setData({
      searchMode: false,
      currentPage: 1,
      products: [],
      scrollTop: 0,
      isLoadingMore: false
    });

    await this.loadProducts();
  },

  /**
   * 加载产品数据
   */
  async loadProducts() {
    if (this.data.loading) return;

    try {
      // 设置加载状态
      const updateData = {
        loading: this.data.currentPage === 1 ? true : false,
        loadingMore: this.data.currentPage > 1 ? true : false
      };

      this.setData(updateData);

      const { searchMode, searchKeyword, selectedProductType, selectedBrand, currentPage, pageSize } = this.data;

      let result;

      if (searchMode && searchKeyword.trim()) {
        // 搜索模式：使用关键字搜索API
        console.log('关键字搜索参数:', {
          keyword: searchKeyword.trim(),
          limit: pageSize
        });

        result = await api.product.searchProducts(
          searchKeyword.trim(),
          pageSize,
          '' // category参数暂时为空
        );
      } else {
        // 筛选模式：使用原有的查询API
        console.log('筛选查询参数:', {
          productType: selectedProductType,
          brandName: selectedBrand,
          page: currentPage,
          limit: pageSize
        });

        result = await api.product.queryProducts(
          selectedProductType,
          selectedBrand,
          currentPage,
          pageSize
        );
      }
      
      console.log('产品查询结果:', result);

      if (result.success) {
        // 两个API返回的数据格式一样，统一处理
        const rawProducts = result.data.products || [];
        const pagination = result.data.pagination || {};

        // 搜索模式下禁用分页功能
        if (searchMode && searchKeyword.trim()) {
          pagination.hasNextPage = false;
        }

        // 预处理产品数据，将中文属性名转换为英文
        const newProducts = rawProducts.map(product => ({
          ...product,
          releaseDate: product['上市日期'] || product.releaseDate || '未知',
          // 确保每个产品都有唯一的ID，用于wx:key
          skuId: product.skuId || product.id || `product_${Date.now()}_${Math.random()}`
        }));

        // 搜索模式下直接替换数据，筛选模式下支持分页
        const products = (searchMode || currentPage === 1) ? newProducts : [...this.data.products, ...newProducts];

        const finalUpdateData = {
          products,
          totalCount: pagination.total || 0,
          hasMore: pagination.hasNextPage || false,
          hasSearched: true,
          loading: false,
          loadingMore: false
        };

        // 如果是第一页或搜索模式，重置滚动位置
        if (currentPage === 1 || searchMode) {
          finalUpdateData.scrollTop = 0;
        }

        this.setData(finalUpdateData);

        // 更新产品列表中的对比状态
        this.updateProductsCompareStatus();

        // 提示信息
        if (newProducts.length === 0 && (currentPage === 1 || searchMode)) {
          const message = searchMode ? '未找到相关产品' : '暂无相关产品';
          wx.showToast({
            title: message,
            icon: 'none'
          });
        } else if (currentPage === 1 || searchMode) {
          const message = searchMode ?
            `找到${newProducts.length}个相关产品` :
            `找到${pagination.total || 0}个产品`;
          wx.showToast({
            title: message,
            icon: 'success'
          });
        } else if (newProducts.length > 0) {
          // 加载更多成功的提示
          console.log(`成功加载第${currentPage}页，新增${newProducts.length}个产品`);

          // 如果是手动加载更多，显示加载成功的数量
          if (currentPage > 1) {
            console.log(`手动加载更多成功：新增${newProducts.length}个产品`);
          }
        }
      } else {
        console.error('产品查询失败:', result);
        const message = searchMode ? '搜索失败' : '查询失败';
        wx.showToast({
          title: result.message || message,
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('加载产品失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'error'
      });
    } finally {
      this.setData({ 
        loading: false,
        loadingMore: false
      });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 加载更多
   */
  async loadMore() {
    if (!this.data.hasMore || this.data.loadingMore || this.data.loading) {
      console.log('无法加载更多:', {
        hasMore: this.data.hasMore,
        loadingMore: this.data.loadingMore,
        loading: this.data.loading
      });
      return;
    }
    
    console.log('开始加载更多数据...');
    
    this.setData({
      currentPage: this.data.currentPage + 1
    });
    
    await this.loadProducts();
  },

  /**
   * 手动加载更多产品 - 用户点击文字链接触发
   */
  async onLoadMoreClick() {
    if (!this.data.hasMore || this.data.loadingMore || this.data.loading) {
      console.log('无法加载更多:', {
        hasMore: this.data.hasMore,
        loadingMore: this.data.loadingMore,
        loading: this.data.loading
      });
      return;
    }
    
    console.log('用户点击查看更多文字链接，开始加载...');
    
    // 显示轻量加载提示
    wx.showToast({
      title: '加载中...',
      icon: 'loading',
      duration: 0 // 设置为0，手动控制隐藏时机
    });
    
    this.setData({
      currentPage: this.data.currentPage + 1
    });
    
    try {
      await this.loadProducts();
      
      // 加载完成后立即隐藏提示
      wx.hideToast();
    } catch (error) {
      console.error('手动加载更多失败:', error);
      
      // 隐藏加载提示
      wx.hideToast();
      
      // 显示错误提示
      wx.showToast({
        title: '加载失败',
        icon: 'error',
        duration: 1500
      });
      
      // 加载失败时回滚页码
      this.setData({
        currentPage: this.data.currentPage - 1
      });
    }
  },

  /**
   * 刷新产品列表
   */
  async refreshProducts() {
    this.setData({
      currentPage: 1,
      products: [],
      scrollTop: 0
    });
    
    await this.loadProducts();
  },

  /**
   * 重置筛选条件
   */
  resetFilters() {
    this.setData({
      // 重置搜索状态
      searchKeyword: '',
      searchMode: false,
      // 重置筛选状态
      productTypeIndex: -1,
      brandIndex: -1,
      selectedProductType: '',
      selectedBrand: '',
      brandOptions: [{ value: '', label: '全部品牌' }],
      // 重置产品列表
      products: [],
      hasSearched: false,
      currentPage: 1,
      totalCount: 0,
      hasMore: false,
      scrollTop: 0
    });

    wx.showToast({
      title: '已重置所有条件',
      icon: 'success'
    });
  },

  /**
   * 产品点击事件
   */
  onProductTap(e) {
    const product = e.detail.product;
    console.log('点击产品:', product);
    
    // 跳转到产品详情页面
    wx.navigateTo({
      url: `/pages/product/product_detail/product_detail?productName=${encodeURIComponent(product.skuName)}`
    });
  },

  /**
   * 图片加载错误处理
   */
  onImageError(e) {
    console.log('图片加载失败:', e);
    // 可以设置默认图片或者隐藏图片
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '选选产品库 - 找到你想要的产品',
      path: '/pages/product/product_library/product_library'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '选选产品库 - 找到你想要的产品'
    };
  },

  // ==================== 产品对比功能 Product Compare Functions ====================

  /**
   * 切换对比面板显示状态
   */
  onToggleCompareVisible(e) {
    const { visible } = e.detail;
    this.setData({
      compareVisible: visible
    });
    console.log('对比面板显示状态:', visible);
  },

  /**
   * 产品对比按钮切换事件
   */
  onCompareToggle(e) {
    const { product } = e.detail;
    console.log('切换产品对比状态:', product);
    
    const app = getApp();
    
    // 检查产品是否已在对比列表中
    const isInCompare = app.isInCompare(product.skuId);
    
    if (isInCompare) {
      // 从对比列表中移除
      const compareProducts = app.getCompareProducts();
      const index = compareProducts.findIndex(p => p.skuId === product.skuId);
      if (index >= 0) {
        app.removeFromCompare(index);
      }
    } else {
      // 添加到对比列表
      app.addToCompare(product);
    }
    
    // 更新产品列表中的对比状态
    this.updateProductsCompareStatus();
  },

  /**
   * 更新产品列表中的对比状态
   */
  updateProductsCompareStatus() {
    const app = getApp();
    const compareProducts = app.getCompareProducts();
    const compareSkuIds = compareProducts.map(p => p.skuId);
    const updatedProducts = this.data.products.map(product => ({
      ...product,
      isInCompare: compareSkuIds.includes(product.skuId)
    }));
    
    this.setData({
      products: updatedProducts
    });
  }
});

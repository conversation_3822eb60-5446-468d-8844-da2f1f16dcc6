const ProductComparisonV4Cache = require('../../models/ProductComparisonV4Cache');
const { compareProductsByNamesV4Parallel } = require('./productCompareParallel');
const { createNotification } = require('../notificationService');
const socketService = require('../socketService');

/**
 * 异步产品对比服务
 * 提供异步AI产品对比功能，支持缓存检查和后台处理
 */

/**
 * 检查产品对比缓存状态并启动异步处理
 * @param {Array<String>} productNames 产品名称列表
 * @param {String} userId 用户ID
 * @returns {Promise<Object>} 缓存状态信息
 */
const checkComparisonCacheStatus = async (productNames, userId) => {
  try {
    console.log('🔍 开始检查产品对比缓存状态');
    console.log('待对比产品:', productNames);
    console.log('用户ID:', userId);

    // 1. 验证输入参数
    if (!Array.isArray(productNames) || productNames.length < 2) {
      return {
        success: false,
        error: '至少需要提供2个产品名称进行对比',
        data: null
      };
    }

    if (productNames.length > 6) {
      return {
        success: false,
        error: '最多支持6个产品同时对比',
        data: null
      };
    }

    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空',
        data: null
      };
    }

    // 2. 检查缓存
    console.log('🔍 检查缓存中是否存在对比结果...');
    const cachedResult = await ProductComparisonV4Cache.findByProductNames(productNames);

    if (cachedResult) {
      console.log('✅ 找到缓存结果');

      return {
        success: true,
        data: {
          hasCache: true,
          isProcessing: false,
          message: '已找到缓存结果，可以直接获取对比数据',
          productNames: productNames,
          comparisonCacheId: cachedResult._id
        }
      };
    }

    console.log('❌ 未找到缓存，启动后台AI分析...');

    // 3. 没有缓存，启动后台处理
    // 立即返回处理中的状态
    const processingResponse = {
      success: true,
      data: {
        hasCache: false,
        isProcessing: true,
        message: '正在生成AI对比分析结果，请大概等待一分钟，完成后会消息通知您',
        estimatedTime: '约1分钟',
        productNames: productNames
      }
    };

    // 4. 启动后台异步处理（不等待结果）
    processComparisonInBackground(productNames, userId).catch(error => {
      console.error('❌ 后台处理失败:', error);
    });

    return processingResponse;

  } catch (error) {
    console.error('❌ 检查缓存状态失败:', error);
    return {
      success: false,
      error: `检查缓存状态失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 后台处理产品对比
 * @param {Array<String>} productNames 产品名称列表
 * @param {String} userId 用户ID
 */
const processComparisonInBackground = async (productNames, userId) => {
  try {
    console.log('🚀 开始后台处理产品对比...');
    
    // 调用原有的并行对比服务
    const result = await compareProductsByNamesV4Parallel(productNames, userId);
    
    if (result.success) {
      console.log('✅ 后台产品对比完成，准备发送通知');
      
      // 发送完成通知
      await sendComparisonCompletedNotification(userId, productNames, result.data.comparisonCacheId);
      
      console.log('✅ 通知发送完成');
    } else {
      console.error('❌ 后台产品对比失败:', result.error);
      
      // 发送失败通知
      await sendComparisonFailedNotification(userId, productNames, result.error);
    }
    
  } catch (error) {
    console.error('❌ 后台处理异常:', error);
    
    // 发送失败通知
    await sendComparisonFailedNotification(userId, productNames, error.message);
  }
};

/**
 * 发送对比完成通知
 * @param {String} userId 用户ID
 * @param {Array<String>} productNames 产品名称列表
 * @param {String} comparisonCacheId 对比缓存ID
 */
const sendComparisonCompletedNotification = async (userId, productNames, comparisonCacheId) => {
  try {
    const productNamesText = productNames.join('、');
    const content = `您请求的产品对比分析已完成！对比产品：${productNamesText}`;
    
    const notification = await createNotification(
      userId,
      null, // 系统通知，无发送者
      'product_comparison_completed',
      content,
      comparisonCacheId,
      'product_comparison',
      {
        productNames: productNames,
        completedAt: new Date()
      }
    );
    socketService.sendNotification(userId, notification);
    console.log('✅ 对比完成通知发送成功');
  } catch (error) {
    console.error('❌ 发送完成通知失败:', error);
  }
};

/**
 * 发送对比失败通知
 * @param {String} userId 用户ID
 * @param {Array<String>} productNames 产品名称列表
 * @param {String} errorMessage 错误信息
 */
const sendComparisonFailedNotification = async (userId, productNames, errorMessage) => {
  try {
    const productNamesText = productNames.join('、');
    const content = `很抱歉，您请求的产品对比分析失败了。对比产品：${productNamesText}。请稍后重试。`;
    
    const notification = await createNotification(
      userId,
      null, // 系统通知，无发送者
      'product_comparison_completed',
      content,
      null,
      'product_comparison',
      {
        productNames: productNames,
        failed: true,
        errorMessage: errorMessage,
        failedAt: new Date()
      }
    );
    socketService.sendNotification(userId, notification);
    console.log('✅ 对比失败通知发送成功');
  } catch (error) {
    console.error('❌ 发送失败通知失败:', error);
  }
};

module.exports = {
  checkComparisonCacheStatus
};

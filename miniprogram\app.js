// app.js
const socketService = require('./utils/socket');
const { notification } = require('./utils/api');

App({
  globalData: {
    userInfo: null,
    token: null,
    isLoggedIn: false,
    systemInfo: {},
    // 开发环境
    baseUrl: 'http://localhost:5000/api/v1', // API基础地址
    // baseUrl: 'http://*************:5000/api/v1',
    socketUrl: 'ws://localhost:5001', // WebSocket地址
    // socketUrl: 'ws://*************:5001',
    // 生产环境
    // baseUrl: 'https://x-xuan.com/api/v1',
    // socketUrl: 'wss://x-xuan.com/socket/',
    eventChannel: null,
    unreadNotificationCount: 0,
    socketConnected: false,  // 添加WebSocket连接状态标志
    socketService: socketService,  // 暴露socketService实例
    // 产品对比相关全局状态
    compareProducts: [], // 对比产品列表
    maxCompareCount: 6   // 最大对比产品数量
  },
  
  onLaunch: function () {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.globalData.systemInfo = systemInfo;
    
    // 检查是否有登录状态
    this.checkLoginStatus();
    
    // 清空产品对比列表
    this.globalData.compareProducts = [];
    
    // 监听页面显示事件
    this.setupPageShowListener();
  },
  
  // 设置页面显示监听器
  setupPageShowListener: function() {
    // 监听页面显示事件
    wx.onAppShow(() => {
      // 当应用重新回到前台时，检查是否需要更新TabBar角标
      if (this.globalData.isLoggedIn) {
        // 延迟执行，确保页面已完全显示
        setTimeout(() => {
          // this.updateTabBarBadge();
          this.updateUnreadCount();
        }, 300);
      }
    });
    
    // 添加页面显示监听到全局事件通道
    this.getEventChannel().on('pageShow', (pagePath) => {
      if (this.globalData.isLoggedIn && this.isTabBarPage(pagePath)) {
        // 当切换到TabBar页面时，更新角标
        this.updateTabBarBadge();
        // this.updateUnreadCount();
      }
    });
  },
  
  // 检查登录状态
  checkLoginStatus: function () {
    try {
      const token = wx.getStorageSync('token');
      const userInfo = wx.getStorageSync('userInfo');
      
      if (token && userInfo) {
        this.globalData.isLoggedIn = true;
        this.globalData.token = token;
        this.globalData.userInfo = userInfo;
        
        // 登录成功后连接WebSocket
        this.connectSocket();
        
        // 获取未读通知数量
        this.updateUnreadCount();
      } else {
        this.globalData.isLoggedIn = false;
        
        // 确保WebSocket连接已关闭
        if (this.globalData.socketConnected) {
          socketService.close();
          this.globalData.socketConnected = false;
        }
      }
    } catch (e) {
      console.error('检查登录状态失败:', e);
      this.globalData.isLoggedIn = false;
      
      // 确保WebSocket连接已关闭
      if (this.globalData.socketConnected) {
        socketService.close();
        this.globalData.socketConnected = false;
      }
    }
  },
  
  // 登录方法
  login: function (userInfo, token) {
    // 先确保之前的连接已关闭
    if (this.globalData.socketConnected) {
      socketService.close();
      this.globalData.socketConnected = false;
    }
    
    // 更新之前记录登录状态变化
    const wasLoggedIn = this.globalData.isLoggedIn;
    
    this.globalData.isLoggedIn = true;
    this.globalData.userInfo = userInfo;
    this.globalData.token = token;
    
    try {
      wx.setStorageSync('userInfo', userInfo);
      wx.setStorageSync('token', token);
      
      // 登录成功后连接WebSocket
      this.connectSocket();
      
      // 获取未读通知数量
      this.updateUnreadCount();
      
      // 如果登录状态发生变化，触发事件
      if (!wasLoggedIn) {
        this.getEventChannel().emit('loginStatusChanged', true);
      }
    } catch (e) {
      console.error('保存登录信息失败:', e);
    }
  },
  
  // 退出登录
  logout: function () {
    // 先断开WebSocket连接
    if (this.globalData.socketConnected) {
      console.log('退出登录: 关闭WebSocket连接');
      socketService.close();
    }

    // 记录之前的登录状态
    const wasLoggedIn = this.globalData.isLoggedIn;

    this.globalData.isLoggedIn = false;
    this.globalData.userInfo = null;
    this.globalData.token = null;
    this.globalData.socketConnected = false;  // 重置连接状态

    // 清空产品对比列表，避免用户数据混合
    this.clearCompareProducts();

    try {
      wx.removeStorageSync('userInfo');
      wx.removeStorageSync('token');

      // 如果登录状态发生变化，触发事件
      if (wasLoggedIn) {
        this.getEventChannel().emit('loginStatusChanged', false);
      }
    } catch (e) {
      console.error('清除登录信息失败:', e);
    }
  },
  
  // 连接WebSocket
  connectSocket: function() {
    try {
      // 如果未登录，不连接WebSocket
      if (!this.globalData.isLoggedIn) {
        console.log('未登录，不连接WebSocket');
        return;
      }
      
      // 检查是否已连接或正在连接中，避免重复连接
      if (this.globalData.socketConnected || socketService.isConnecting) {
        console.log('WebSocket已连接或正在连接中，无需重新连接');
        return;
      }
      
      // 从存储中获取token
      const token = this.globalData.token || wx.getStorageSync('token');
      if (!token) {
        console.log('未找到token，无法连接WebSocket');
        return;
      }
      
      // 初始化WebSocket事件监听
      this.initSocketListeners();
      
      // 提取token字符串
      // 根据token的存储格式判断token字符串
      const tokenStr = typeof token === 'string' 
                      ? token 
                      : (token.accessToken || (typeof token === 'object' ? JSON.stringify(token) : String(token)));
      
      // 连接WebSocket
      socketService.connect(tokenStr)
        .then(() => {
          console.log('WebSocket连接成功');
          this.globalData.socketConnected = true;  // 标记为已连接
        })
        .catch(error => {
          console.error('WebSocket连接失败:', error);
          this.globalData.socketConnected = false;
        });
    } catch (err) {
      console.error('连接WebSocket时出错:', err);
      this.globalData.socketConnected = false;
    }
  },
  
  // 初始化WebSocket事件监听
  initSocketListeners: function() {
    // 添加通知监听
    socketService.off('notification');
    socketService.on('notification', (notificationData) => {
      this.handleNotification(notificationData);
    });
    
    // 添加连接状态监听
    socketService.off('connection');
    socketService.on('connection', (data) => {
      if (data && data.status === 'connected') {
        this.globalData.socketConnected = true;
      } else if (data && data.status === 'disconnected') {
        this.globalData.socketConnected = false;
      }
    });
  },
  
  // 处理通知消息
  handleNotification: function(data) {
    console.log('收到新通知:', data);
    
    // 更新未读通知计数
    console.log('更新未读通知计数');
    this.updateUnreadCount();
    
    // 显示通知提醒
    wx.showToast({
      title: '收到新通知',
      icon: 'none',
      duration: 2000
    });
    
    // 发送通知事件，页面可以监听此事件
    this.getEventChannel().emit('newNotification', data);
  },
  
  // 页面显示回调，供TabBar页面的onShow调用
  onPageShow: function(pagePath) {
    // 发送页面显示事件
    this.getEventChannel().emit('pageShow', pagePath);
  },
  
  // 获取事件通道
  getEventChannel: function() {
    if (!this.globalData.eventChannel) {
      this.globalData.eventChannel = new this.EventChannel();
    }
    return this.globalData.eventChannel;
  },
  
  // 更新未读通知计数
  updateUnreadCount: function() {
    if (!this.globalData.isLoggedIn) return;
    
    notification.getUnreadCount()
      .then(res => {
        if (res && res.success) {
          console.log("未读通知数量为", res.data.count);
          this.globalData.unreadNotificationCount = res.data.count;
          
          // 发送未读计数更新事件
          this.getEventChannel().emit('unreadCountUpdated', this.globalData.unreadNotificationCount);
          
          // 更新TabBar的角标
          this.updateTabBarBadge();
        }
      })
      .catch(error => {
        console.error('获取未读通知数量失败:', error);
      });
  },
  
  // 更新TabBar角标
  updateTabBarBadge: function() {
    const count = this.globalData.unreadNotificationCount;
    const tabIndex = 1; // 假设"消息"选项卡的索引是1，根据实际情况调整
    
    // 获取当前页面路径
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentPagePath = currentPage ? currentPage.route : '';
    
    // 检查当前页面是否是TabBar页面
    const isTabBarPage = this.isTabBarPage(currentPagePath);
    
    console.log('更新TabBar角标:', {
      count: count,
      currentPage: currentPagePath,
      isTabBarPage: isTabBarPage
    });
    
    if (count > 0) {
      if (isTabBarPage) {
        // 仅在TabBar页面上尝试设置角标
        console.log('在TabBar页面设置角标:', count);
        wx.setTabBarBadge({
          index: tabIndex,
          text: count.toString(),
          fail: (error) => {
            // 只有在TabBar页面上才输出错误日志
            console.error('设置TabBar角标失败:', error);
          }
        });
      } else {
        // 非TabBar页面，只记录未读数量，不尝试设置角标
        console.log('当前非TabBar页面，保存未读消息数量:', count);
      }
    } else {
      if (isTabBarPage) {
        // 仅在TabBar页面上尝试移除角标
        console.log('在TabBar页面移除角标');
        wx.removeTabBarBadge({
          index: tabIndex,
          fail: (error) => {
            // 只在开发环境下输出低级别日志，不作为错误处理
            console.log('移除TabBar角标结果:', error.errMsg);
          }
        });
      } else {
        // 非TabBar页面，只记录未读数量为0
        console.log('当前非TabBar页面，标记未读消息数量为0');
      }
    }
  },
  
  // 判断页面是否是TabBar页面
  isTabBarPage: function(pagePath) {
    if (!pagePath) return false;
    
    // 从app.json中获取tabBar配置的页面路径列表
    const tabBarPages = [
      'pages/index/index',
      'pages/notifications/index',
      'pages/user/user',
      'pages/product/product_library/product_library',
      'pages/product/ai-recommend/ai-recommend'
    ];
    
    // 检查当前页面是否在tabBar页面列表中
    return tabBarPages.some(tabPage => pagePath === tabPage);
  },

  // ==================== 产品对比全局状态管理 ====================

  // 添加产品到对比列表
  addToCompare: function(product) {
    if (!product || !product.skuId) {
      console.error('无效的产品数据');
      return false;
    }
    
    // 检查产品是否已在对比列表中
    const isExist = this.globalData.compareProducts.some(p => p.skuId === product.skuId);
    if (isExist) {
      wx.showToast({
        title: '产品已在对比列表中',
        icon: 'none'
      });
      return false;
    }
    
    // 检查是否超过最大对比数量
    if (this.globalData.compareProducts.length >= this.globalData.maxCompareCount) {
      wx.showToast({
        title: `最多只能对比${this.globalData.maxCompareCount}个产品`,
        icon: 'none'
      });
      return false;
    }
    
    // 添加产品到对比列表
    this.globalData.compareProducts.push(product);
    

    
    // 发送对比列表更新事件
    this.getEventChannel().emit('compareProductsUpdated', {
      action: 'add',
      product: product,
      compareProducts: this.globalData.compareProducts
    });
    
    wx.showToast({
      title: '已加入对比',
      icon: 'success'
    });
    
    console.log('添加产品到对比列表:', product.skuName);
    return true;
  },
  
  // 从对比列表移除产品
  removeFromCompare: function(index) {
    if (index < 0 || index >= this.globalData.compareProducts.length) {
      console.error('无效的产品索引:', index);
      return false;
    }
    
    const removedProduct = this.globalData.compareProducts[index];
    this.globalData.compareProducts.splice(index, 1);
    
    // 发送对比列表更新事件
    this.getEventChannel().emit('compareProductsUpdated', {
      action: 'remove',
      product: removedProduct,
      index: index,
      compareProducts: this.globalData.compareProducts
    });
    
    console.log('从对比列表移除产品:', removedProduct.skuName);
    return true;
  },
  
  // 清空对比列表
  clearCompareProducts: function() {
    this.globalData.compareProducts = [];
    
    // 发送对比列表更新事件
    this.getEventChannel().emit('compareProductsUpdated', {
      action: 'clear',
      compareProducts: []
    });
    
    console.log('已清空对比列表');
  },
  
  // 获取对比产品列表
  getCompareProducts: function() {
    return this.globalData.compareProducts;
  },
  
  // 检查产品是否在对比列表中
  isInCompare: function(skuId) {
    return this.globalData.compareProducts.some(p => p.skuId === skuId);
  },
  

  
  // 开始传统对比
  startTraditionalCompare: function() {
    const products = this.globalData.compareProducts;
    
    if (products.length < 2) {
      wx.showToast({
        title: '至少选择2个产品进行对比',
        icon: 'none'
      });
      return;
    }
    
    if (products.length > 6) {
      wx.showToast({
        title: '传统对比最多支持6个产品',
        icon: 'none'
      });
      return;
    }
    
    console.log('开始传统对比产品:', products);
    
    // 构建传统对比页面参数
    const productNames = products.map(p => p.skuName);
    
    // 跳转到传统对比页面
    wx.navigateTo({
      url: `/pages/product/product_compare_basic/product_compare_basic?productNames=${encodeURIComponent(JSON.stringify(productNames))}`
    });
  },
  
  // 开始AI智能对比
  startAICompare: async function() {
    const products = this.globalData.compareProducts;

    if (products.length < 2) {
      wx.showToast({
        title: '至少选择2个产品进行对比',
        icon: 'none'
      });
      return;
    }

    if (products.length > 6) {
      wx.showToast({
        title: 'AI对比最多支持6个产品',
        icon: 'none'
      });
      return;
    }

    console.log('开始AI对比产品:', products);

    // 构建产品名称列表
    const productNames = products.map(p => p.skuName);

    try {
      // 显示加载提示
      wx.showLoading({
        title: '检查对比状态...',
        mask: true
      });

      // 检查缓存状态
      const { product } = require('./utils/api');
      const cacheResult = await product.checkComparisonCache(productNames);

      // 隐藏加载提示
      wx.hideLoading();

      if (cacheResult.success && cacheResult.data.hasCache) {
        // 有缓存，直接跳转到展示页面
        console.log('找到缓存结果，直接跳转到展示页面');
        wx.navigateTo({
          url: `/pages/product/product_compare_v4/product_compare_v4?productNames=${encodeURIComponent(JSON.stringify(productNames))}`
        });
      } else {
        // 无缓存，显示等待提示
        console.log('未找到缓存，启动后台处理');
        wx.showModal({
          title: '对比分析进行中',
          content: '您请求的产品对比分析已在后台进行中，大概需要等待一分钟，完成后会通过通知页面通知您。',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#007AFF'
        });
      }
    } catch (error) {
      // 隐藏加载提示
      wx.hideLoading();

      console.error('检查对比缓存失败:', error);
      wx.showToast({
        title: error.message || '检查对比状态失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },
  
  // EventChannel类，用于页面间通信
  EventChannel: function() {
    this.listeners = {};
    
    this.emit = function(eventName, data) {
      if (this.listeners[eventName]) {
        this.listeners[eventName].forEach(callback => {
          callback(data);
        });
      }
    };
    
    this.on = function(eventName, callback) {
      if (!this.listeners[eventName]) {
        this.listeners[eventName] = [];
      }
      this.listeners[eventName].push(callback);
    };
    
    this.off = function(eventName, callback) {
      if (this.listeners[eventName]) {
        if (callback) {
          this.listeners[eventName] = this.listeners[eventName].filter(
            listener => listener !== callback
          );
        } else {
          delete this.listeners[eventName];
        }
      }
    };
  }
})